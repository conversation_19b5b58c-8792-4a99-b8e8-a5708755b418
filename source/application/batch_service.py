import asyncio
import json
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from contextlib import suppress
import logging

from source.module.model import TaskStatus, TaskInfo, BatchExtractParams
from source.translation import _


class BatchProcessingService:
    """批量处理服务，管理多个下载任务"""
    
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.processing_queue = asyncio.Queue()
        self.max_concurrent_tasks = 3  # 最大并发任务数
        self.worker_tasks: List[asyncio.Task] = []
        self.is_running = False
        
    async def start_workers(self):
        """启动工作线程"""
        if self.is_running:
            return
            
        self.is_running = True
        # 创建多个工作线程来处理任务
        for i in range(self.max_concurrent_tasks):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker)
    
    async def stop_workers(self):
        """停止工作线程"""
        self.is_running = False
        
        # 取消所有工作任务
        for task in self.worker_tasks:
            task.cancel()
        
        # 等待所有任务完成
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
    
    async def submit_batch_task(
        self, 
        params: BatchExtractParams, 
        xhs_instance
    ) -> str:
        """提交批量处理任务"""
        task_id = str(uuid.uuid4())
        current_time = datetime.now().isoformat()
        
        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            status=TaskStatus.PENDING,
            total_urls=len(params.urls),
            processed_urls=0,
            failed_urls=0,
            created_at=current_time,
            updated_at=current_time,
            results=[],
            errors=[]
        )
        
        self.tasks[task_id] = task_info
        
        # 将任务添加到处理队列
        await self.processing_queue.put({
            'task_id': task_id,
            'params': params,
            'xhs_instance': xhs_instance
        })
        
        return task_id
    
    async def get_task_status(self, task_id: str) -> Optional[TaskInfo]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    async def get_all_tasks(self) -> List[TaskInfo]:
        """获取所有任务状态"""
        return list(self.tasks.values())

    async def remove_task(self, task_id: str) -> bool:
        """删除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            return True
        return False
    
    async def _worker(self, worker_name: str):
        """工作线程，处理队列中的任务"""
        logging.info(f"Worker {worker_name} started")
        
        while self.is_running:
            try:
                # 从队列中获取任务，超时1秒
                task_data = await asyncio.wait_for(
                    self.processing_queue.get(), 
                    timeout=1.0
                )
                
                await self._process_batch_task(task_data, worker_name)
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except asyncio.CancelledError:
                # 工作线程被取消
                break
            except Exception as e:
                logging.error(f"Worker {worker_name} error: {e}")
                continue
        
        logging.info(f"Worker {worker_name} stopped")
    
    async def _process_batch_task(self, task_data: dict, worker_name: str):
        """处理单个批量任务"""
        task_id = task_data['task_id']
        params = task_data['params']
        xhs_instance = task_data['xhs_instance']
        
        task_info = self.tasks.get(task_id)
        if not task_info:
            return
        
        logging.info(f"Worker {worker_name} processing task {task_id}")
        
        # 更新任务状态为处理中
        task_info.status = TaskStatus.PROCESSING
        task_info.updated_at = datetime.now().isoformat()
        
        try:
            # 处理每个URL
            for url in params.urls:
                try:
                    # 直接调用XHS的内部方法处理单个URL
                    result = await xhs_instance._XHS__deal_extract(
                        url,
                        params.download,
                        params.index,
                        None,  # log
                        None,  # bar
                        not params.skip,  # data
                        params.cookie,
                        params.proxy,
                    )

                    if result and result != {}:
                        task_info.results.append({
                            'url': url,
                            'status': 'success',
                            'data': result
                        })
                        task_info.processed_urls += 1
                    else:
                        task_info.results.append({
                            'url': url,
                            'status': 'failed',
                            'error': _("获取数据失败")
                        })
                        task_info.failed_urls += 1
                        task_info.errors.append(f"URL {url}: {_('获取数据失败')}")
                
                except Exception as e:
                    error_msg = f"URL {url}: {str(e)}"
                    task_info.results.append({
                        'url': url,
                        'status': 'failed',
                        'error': str(e)
                    })
                    task_info.failed_urls += 1
                    task_info.errors.append(error_msg)
                    logging.error(f"Error processing URL {url}: {e}")
                
                # 更新任务状态
                task_info.updated_at = datetime.now().isoformat()
                
                # 添加小延迟避免过于频繁的请求
                await asyncio.sleep(0.5)
            
            # 任务完成
            task_info.status = TaskStatus.COMPLETED
            task_info.updated_at = datetime.now().isoformat()
            
            logging.info(f"Task {task_id} completed. Processed: {task_info.processed_urls}, Failed: {task_info.failed_urls}")
            
        except Exception as e:
            # 任务失败
            task_info.status = TaskStatus.FAILED
            task_info.updated_at = datetime.now().isoformat()
            error_msg = f"Task failed: {str(e)}"
            task_info.errors.append(error_msg)
            logging.error(f"Task {task_id} failed: {e}")
    
    async def cleanup_old_tasks(self, max_age_hours: int = 24):
        """清理旧任务"""
        current_time = datetime.now()
        tasks_to_remove = []
        
        for task_id, task_info in self.tasks.items():
            created_time = datetime.fromisoformat(task_info.created_at)
            age_hours = (current_time - created_time).total_seconds() / 3600
            
            if age_hours > max_age_hours:
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.tasks[task_id]
        
        if tasks_to_remove:
            logging.info(f"Cleaned up {len(tasks_to_remove)} old tasks")


# 全局批量处理服务实例
batch_service = BatchProcessingService()

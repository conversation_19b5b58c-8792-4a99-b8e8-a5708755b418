"""
GUI应用程序入口
"""

import sys
import asyncio
import qasync
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from .main_window import MainWindow


class XHSGuiApplication:
    """XHS GUI应用程序"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.event_loop = None
    
    def setup_application(self):
        """设置应用程序"""
        # 创建QApplication
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("XHS-Downloader GUI")
        self.app.setApplicationVersion("1.0")
        self.app.setOrganizationName("XHS-Downloader")
        
        # 设置应用程序属性
        self.app.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        self.app.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        # 设置事件循环
        self.event_loop = qasync.QEventLoop(self.app)
        asyncio.set_event_loop(self.event_loop)
        
        # 创建主窗口
        self.main_window = MainWindow()
    
    def run(self):
        """运行应用程序"""
        try:
            # 设置应用程序
            self.setup_application()
            
            # 显示主窗口
            self.main_window.show()
            
            # 运行事件循环
            with self.event_loop:
                return self.event_loop.run_forever()
                
        except Exception as e:
            print(f"应用程序运行失败: {e}")
            if self.app:
                QMessageBox.critical(None, "错误", f"应用程序运行失败: {e}")
            return 1
        finally:
            if self.app:
                self.app.quit()


def main():
    """主函数"""
    app = XHSGuiApplication()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())

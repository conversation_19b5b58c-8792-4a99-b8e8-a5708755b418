"""
主窗口
"""

import asyncio
import sys
import traceback
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QPushButton, QLabel, QMessageBox,
    QStatusBar, QSplitter, QTextEdit, QGroupBox
)
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QFont, QIcon

from .config_widget import ConfigWidget
from .task_widget import TaskWidget
from .config_manager import config_manager
from .server_manager import ServerManager
from .styles import COMPLETE_STYLE


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.server_manager = ServerManager()
        self.init_ui()
        self.setup_connections()
        self.load_config()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("XHS-Downloader GUI")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # 应用样式
        self.setStyleSheet(COMPLETE_STYLE)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧配置面板
        config_panel = self.create_config_panel()
        splitter.addWidget(config_panel)
        
        # 右侧任务面板
        task_panel = self.create_task_panel()
        splitter.addWidget(task_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 600])
        main_layout.addWidget(splitter)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_config_panel(self):
        """创建配置面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题
        title_label = QLabel("配置管理")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 配置组件
        self.config_widget = ConfigWidget()
        layout.addWidget(self.config_widget)
        
        # 服务器控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("启动服务")
        self.start_button.setProperty("class", "success")
        self.start_button.clicked.connect(self.start_server)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止服务")
        self.stop_button.setProperty("class", "danger")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_server)
        button_layout.addWidget(self.stop_button)
        
        layout.addLayout(button_layout)
        
        return panel
    
    def create_task_panel(self):
        """创建任务面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 任务管理组件
        self.task_widget = TaskWidget()
        layout.addWidget(self.task_widget)
        
        # 日志区域
        log_group = QGroupBox("服务器日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        return panel
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 服务器状态
        self.server_status_label = QLabel("服务器: 未启动")
        self.status_bar.addWidget(self.server_status_label)
        
        self.status_bar.addPermanentWidget(QLabel("XHS-Downloader GUI v1.0"))
    
    def setup_connections(self):
        """设置信号连接"""
        # 配置改变信号
        self.config_widget.config_changed.connect(self.on_config_changed)
    
    def load_config(self):
        """加载配置"""
        try:
            config = config_manager.get_config()
            self.log_message(f"配置加载成功: {config_manager.config_file}")
        except Exception as e:
            self.log_message(f"配置加载失败: {e}")
            QMessageBox.warning(self, "警告", f"配置加载失败: {e}")
    
    def on_config_changed(self):
        """配置改变事件"""
        self.log_message("配置已更新")
    
    def start_server(self):
        """启动服务器"""
        try:
            # 验证配置
            is_valid, message = config_manager.validate_config()
            if not is_valid:
                QMessageBox.warning(self, "配置错误", message)
                return
            
            # 更新界面状态
            self.start_button.setEnabled(False)
            self.start_button.setText("启动中...")
            self.server_status_label.setText("服务器: 启动中...")
            
            # 启动服务器
            config = config_manager.get_config()
            asyncio.create_task(self.do_start_server(config))
            
        except Exception as e:
            self.log_message(f"启动服务器失败: {e}")
            QMessageBox.critical(self, "错误", f"启动服务器失败: {e}")
            self.reset_server_ui()
    
    async def do_start_server(self, config):
        """执行启动服务器"""
        try:
            await self.server_manager.start_server()

            # 更新界面状态
            self.start_button.setEnabled(False)
            self.start_button.setText("启动服务")
            self.stop_button.setEnabled(True)
            self.server_status_label.setText(f"服务器: 运行中 ({config.server_host}:{config.server_port})")

            # 开始监控任务
            self.task_widget.start_monitoring()

            self.log_message(f"服务器启动成功: {config.server_host}:{config.server_port}")

        except Exception as e:
            self.log_message(f"启动服务器失败: {e}")
            QMessageBox.critical(self, "错误", f"启动服务器失败: {e}")
            self.reset_server_ui()
    
    def stop_server(self):
        """停止服务器"""
        try:
            # 更新界面状态
            self.stop_button.setEnabled(False)
            self.stop_button.setText("停止中...")
            self.server_status_label.setText("服务器: 停止中...")
            
            # 停止任务监控
            self.task_widget.stop_monitoring()
            
            # 停止服务器
            asyncio.create_task(self.do_stop_server())
            
        except Exception as e:
            self.log_message(f"停止服务器失败: {e}")
            QMessageBox.critical(self, "错误", f"停止服务器失败: {e}")
    
    async def do_stop_server(self):
        """执行停止服务器"""
        try:
            await self.server_manager.stop_server()
            
            # 重置界面状态
            self.reset_server_ui()
            
            self.log_message("服务器已停止")
            
        except Exception as e:
            self.log_message(f"停止服务器失败: {e}")
            QMessageBox.critical(self, "错误", f"停止服务器失败: {e}")
    
    def reset_server_ui(self):
        """重置服务器界面状态"""
        self.start_button.setEnabled(True)
        self.start_button.setText("启动服务")
        self.stop_button.setEnabled(False)
        self.stop_button.setText("停止服务")
        self.server_status_label.setText("服务器: 未启动")
    
    def log_message(self, message):
        """记录日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        self.log_text.append(log_entry)
        print(log_entry)  # 同时输出到控制台
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.server_manager.is_running:
            reply = QMessageBox.question(
                self, "确认退出", "服务器正在运行，确定要退出吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.No:
                event.ignore()
                return
            
            # 停止服务器
            asyncio.create_task(self.server_manager.stop_server())
        
        # 停止任务监控
        self.task_widget.stop_monitoring()
        
        event.accept()

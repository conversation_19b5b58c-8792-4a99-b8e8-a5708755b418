"""
服务器管理器
"""

import asyncio
import threading
import uvicorn
from typing import Optional

from ..application.batch_service import batch_service
from .config_manager import config_manager


class ServerManager:
    """服务器管理器"""
    
    def __init__(self):
        self.server: Optional[uvicorn.Server] = None
        self.server_thread: Optional[threading.Thread] = None
        self.is_running = False
        self.xhs_instance = None
    
    async def start_server(self):
        """启动服务器"""
        if self.is_running:
            raise RuntimeError("服务器已在运行")
        
        try:
            # 获取配置
            config = config_manager.get_config()
            
            # 创建XHS实例
            from .. import XHS
            xhs_kwargs = config_manager.get_xhs_kwargs()
            self.xhs_instance = XHS(**xhs_kwargs)
            
            # 启动XHS实例
            await self.xhs_instance.__aenter__()
            
            # 启动批量服务
            await batch_service.start()
            
            # 创建服务器配置
            server_config = uvicorn.Config(
                "source.application.app:app",
                host=config.server_host,
                port=config.server_port,
                log_level=config.log_level,
                access_log=False,  # 禁用访问日志以减少输出
                loop="asyncio"
            )
            
            # 创建服务器实例
            self.server = uvicorn.Server(server_config)
            
            # 在新线程中启动服务器
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()
            
            # 等待服务器启动
            await asyncio.sleep(1)
            
            self.is_running = True
            print(f"✅ 服务器启动成功: {config.server_host}:{config.server_port}")
            
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
            await self.cleanup()
            raise e
    
    def _run_server(self):
        """在线程中运行服务器"""
        try:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行服务器
            loop.run_until_complete(self.server.serve())
            
        except Exception as e:
            print(f"服务器线程错误: {e}")
        finally:
            # 清理事件循环
            try:
                loop.close()
            except:
                pass
    
    async def stop_server(self):
        """停止服务器"""
        if not self.is_running:
            return
        
        try:
            self.is_running = False
            
            # 停止服务器
            if self.server:
                self.server.should_exit = True
                
                # 等待服务器线程结束
                if self.server_thread and self.server_thread.is_alive():
                    self.server_thread.join(timeout=5)
            
            # 清理资源
            await self.cleanup()
            
            print("✅ 服务器已停止")
            
        except Exception as e:
            print(f"❌ 停止服务器失败: {e}")
            raise e
    
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止批量服务
            await batch_service.stop()
            
            # 停止XHS实例
            if self.xhs_instance:
                await self.xhs_instance.__aexit__(None, None, None)
                self.xhs_instance = None
            
        except Exception as e:
            print(f"清理资源失败: {e}")
    
    def get_server_info(self):
        """获取服务器信息"""
        if self.is_running and self.server:
            config = config_manager.get_config()
            return {
                'host': config.server_host,
                'port': config.server_port,
                'status': 'running'
            }
        return {
            'host': '',
            'port': 0,
            'status': 'stopped'
        }

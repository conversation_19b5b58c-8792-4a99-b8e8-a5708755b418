"""
任务管理界面组件
"""

import asyncio
from datetime import datetime
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QHeaderView, QPushButton, QLabel, QProgressBar, QMessageBox,
    QTextEdit, QSplitter, QGroupBox
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QColor, QFont

from ..application.batch_service import batch_service
from ..module.model import TaskStatus


class TaskUpdateThread(QThread):
    """任务更新线程"""

    task_updated = Signal(list)  # 任务更新信号
    
    def __init__(self):
        super().__init__()
        self.running = False
    
    def run(self):
        """运行线程"""
        self.running = True
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            while self.running:
                # 获取所有任务
                tasks = loop.run_until_complete(batch_service.get_all_tasks())
                self.task_updated.emit(tasks)
                
                # 等待2秒
                for _ in range(20):  # 2秒 = 20 * 0.1秒
                    if not self.running:
                        break
                    self.msleep(100)
        except Exception as e:
            print(f"任务更新线程错误: {e}")
        finally:
            loop.close()
    
    def stop(self):
        """停止线程"""
        self.running = False


class TaskWidget(QWidget):
    """任务管理界面组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.update_thread = None
        self.init_ui()
        self.setup_update_timer()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 标题和控制按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("任务管理")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.refresh_tasks)
        header_layout.addWidget(self.refresh_button)
        
        self.clear_button = QPushButton("清空已完成")
        self.clear_button.setProperty("class", "warning")
        self.clear_button.clicked.connect(self.clear_completed_tasks)
        header_layout.addWidget(self.clear_button)
        
        layout.addLayout(header_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 任务表格
        self.task_table = QTableWidget()
        self.setup_task_table()
        splitter.addWidget(self.task_table)
        
        # 任务详情
        details_group = QGroupBox("任务详情")
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setMaximumHeight(150)
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        splitter.addWidget(details_group)
        
        # 设置分割器比例
        splitter.setSizes([400, 150])
        layout.addWidget(splitter)
        
        # 状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.total_tasks_label = QLabel("总任务: 0")
        status_layout.addWidget(self.total_tasks_label)
        
        self.running_tasks_label = QLabel("运行中: 0")
        status_layout.addWidget(self.running_tasks_label)
        
        self.completed_tasks_label = QLabel("已完成: 0")
        status_layout.addWidget(self.completed_tasks_label)
        
        layout.addLayout(status_layout)
    
    def setup_task_table(self):
        """设置任务表格"""
        # 设置列
        columns = ["任务ID", "状态", "总数", "已处理", "失败", "进度", "创建时间", "更新时间"]
        self.task_table.setColumnCount(len(columns))
        self.task_table.setHorizontalHeaderLabels(columns)
        
        # 设置表格属性
        self.task_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.task_table.setAlternatingRowColors(True)
        self.task_table.setSortingEnabled(True)
        
        # 设置列宽
        header = self.task_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # 任务ID
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Fixed)  # 状态
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # 总数
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # 已处理
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Fixed)  # 失败
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)  # 进度
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # 创建时间
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)  # 更新时间
        
        self.task_table.setColumnWidth(0, 120)  # 任务ID
        self.task_table.setColumnWidth(1, 80)   # 状态
        self.task_table.setColumnWidth(2, 60)   # 总数
        self.task_table.setColumnWidth(3, 80)   # 已处理
        self.task_table.setColumnWidth(4, 60)   # 失败
        self.task_table.setColumnWidth(6, 140)  # 创建时间
        self.task_table.setColumnWidth(7, 140)  # 更新时间
        
        # 连接选择信号
        self.task_table.itemSelectionChanged.connect(self.on_task_selected)
    
    def setup_update_timer(self):
        """设置更新定时器"""
        self.update_thread = TaskUpdateThread()
        self.update_thread.task_updated.connect(self.update_task_table)
    
    def start_monitoring(self):
        """开始监控任务"""
        if self.update_thread and not self.update_thread.isRunning():
            self.update_thread.start()
            self.status_label.setText("监控中...")
    
    def stop_monitoring(self):
        """停止监控任务"""
        if self.update_thread and self.update_thread.isRunning():
            self.update_thread.stop()
            self.update_thread.wait()
            self.status_label.setText("已停止")
    
    def update_task_table(self, tasks):
        """更新任务表格"""
        try:
            # 保存当前选择
            current_row = self.task_table.currentRow()
            current_task_id = None
            if current_row >= 0:
                current_task_id = self.task_table.item(current_row, 0).text()
            
            # 清空表格
            self.task_table.setRowCount(0)
            
            # 添加任务
            for task in tasks:
                self.add_task_row(task)
            
            # 恢复选择
            if current_task_id:
                for row in range(self.task_table.rowCount()):
                    if self.task_table.item(row, 0).text() == current_task_id:
                        self.task_table.selectRow(row)
                        break
            
            # 更新统计信息
            self.update_statistics(tasks)
            
        except Exception as e:
            print(f"更新任务表格错误: {e}")
    
    def add_task_row(self, task):
        """添加任务行"""
        row = self.task_table.rowCount()
        self.task_table.insertRow(row)
        
        # 任务ID
        task_id_item = QTableWidgetItem(task.task_id)
        self.task_table.setItem(row, 0, task_id_item)
        
        # 状态
        status_item = QTableWidgetItem(self.get_status_text(task.status))
        status_item.setBackground(self.get_status_color(task.status))
        self.task_table.setItem(row, 1, status_item)
        
        # 总数
        total_item = QTableWidgetItem(str(task.total_urls))
        total_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.task_table.setItem(row, 2, total_item)
        
        # 已处理
        processed_item = QTableWidgetItem(str(task.processed_urls))
        processed_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.task_table.setItem(row, 3, processed_item)
        
        # 失败
        failed_item = QTableWidgetItem(str(task.failed_urls))
        failed_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.task_table.setItem(row, 4, failed_item)
        
        # 进度
        progress_item = QTableWidgetItem()
        if task.total_urls > 0:
            progress = (task.processed_urls / task.total_urls) * 100
            progress_item.setText(f"{progress:.1f}%")
        else:
            progress_item.setText("0%")
        progress_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.task_table.setItem(row, 5, progress_item)
        
        # 创建时间
        created_item = QTableWidgetItem(self.format_time(task.created_at))
        self.task_table.setItem(row, 6, created_item)
        
        # 更新时间
        updated_item = QTableWidgetItem(self.format_time(task.updated_at))
        self.task_table.setItem(row, 7, updated_item)

    def get_status_text(self, status):
        """获取状态文本"""
        status_map = {
            TaskStatus.PENDING: "等待中",
            TaskStatus.PROCESSING: "处理中",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "失败"
        }
        return status_map.get(status, str(status))

    def get_status_color(self, status):
        """获取状态颜色"""
        color_map = {
            TaskStatus.PENDING: QColor(255, 193, 7),      # 黄色
            TaskStatus.PROCESSING: QColor(0, 123, 255),   # 蓝色
            TaskStatus.COMPLETED: QColor(40, 167, 69),    # 绿色
            TaskStatus.FAILED: QColor(220, 53, 69)        # 红色
        }
        return color_map.get(status, QColor(108, 117, 125))  # 灰色

    def format_time(self, time_str):
        """格式化时间"""
        try:
            if isinstance(time_str, str):
                # 解析ISO格式时间
                dt = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                return dt.strftime("%m-%d %H:%M:%S")
            return str(time_str)
        except:
            return str(time_str)

    def update_statistics(self, tasks):
        """更新统计信息"""
        total = len(tasks)
        running = sum(1 for task in tasks if task.status == TaskStatus.PROCESSING)
        completed = sum(1 for task in tasks if task.status == TaskStatus.COMPLETED)

        self.total_tasks_label.setText(f"总任务: {total}")
        self.running_tasks_label.setText(f"运行中: {running}")
        self.completed_tasks_label.setText(f"已完成: {completed}")

    def on_task_selected(self):
        """任务选择事件"""
        current_row = self.task_table.currentRow()
        if current_row >= 0:
            task_id = self.task_table.item(current_row, 0).text()
            asyncio.create_task(self.show_task_details(task_id))

    async def show_task_details(self, task_id):
        """显示任务详情"""
        try:
            task = await batch_service.get_task_status(task_id)
            if task:
                details = f"任务ID: {task.task_id}\n"
                details += f"状态: {self.get_status_text(task.status)}\n"
                details += f"总链接数: {task.total_urls}\n"
                details += f"已处理: {task.processed_urls}\n"
                details += f"失败数: {task.failed_urls}\n"
                details += f"创建时间: {task.created_at}\n"
                details += f"更新时间: {task.updated_at}\n\n"

                if task.errors:
                    details += "错误信息:\n"
                    for error in task.errors[-5:]:  # 只显示最近5个错误
                        details += f"- {error}\n"

                if task.results:
                    details += f"\n处理结果 (显示前10个):\n"
                    for i, result in enumerate(task.results[:10], 1):
                        status = "✅" if result['status'] == 'success' else "❌"
                        details += f"{i}. {status} {result['url']}\n"
                        if result['status'] == 'failed' and 'error' in result:
                            details += f"   错误: {result['error']}\n"

                self.details_text.setText(details)
            else:
                self.details_text.setText("无法获取任务详情")
        except Exception as e:
            self.details_text.setText(f"获取任务详情失败: {e}")

    def refresh_tasks(self):
        """刷新任务"""
        asyncio.create_task(self.do_refresh_tasks())

    async def do_refresh_tasks(self):
        """执行刷新任务"""
        try:
            tasks = await batch_service.get_all_tasks()
            self.update_task_table(tasks)
            self.status_label.setText("刷新完成")
        except Exception as e:
            self.status_label.setText(f"刷新失败: {e}")

    def clear_completed_tasks(self):
        """清空已完成任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要清空所有已完成的任务吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            asyncio.create_task(self.do_clear_completed_tasks())

    async def do_clear_completed_tasks(self):
        """执行清空已完成任务"""
        try:
            # 获取所有任务
            tasks = await batch_service.get_all_tasks()

            # 删除已完成的任务
            cleared_count = 0
            for task in tasks:
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                    if hasattr(batch_service, 'remove_task'):
                        await batch_service.remove_task(task.task_id)
                        cleared_count += 1

            # 刷新任务列表
            await self.do_refresh_tasks()

            self.status_label.setText(f"已清空 {cleared_count} 个任务")

        except Exception as e:
            self.status_label.setText(f"清空任务失败: {e}")
            QMessageBox.critical(self, "错误", f"清空任务失败: {e}")

    def closeEvent(self, event):
        """关闭事件"""
        self.stop_monitoring()
        super().closeEvent(event)

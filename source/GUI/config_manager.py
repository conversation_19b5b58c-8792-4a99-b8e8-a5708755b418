"""
配置管理器 - 管理应用程序配置
"""

import json
import os
from typing import Dict, Any
from dataclasses import dataclass, asdict


@dataclass
class XHSConfig:
    """XHS下载器配置类"""
    
    # 基本配置
    work_path: str = "D:/Download"
    folder_name: str = "XHS_Batch_Downloads"
    name_format: str = "作品ID"
    
    # 网络配置
    user_agent: str = ""
    cookie: str = ""
    proxy: str = ""
    timeout: int = 10
    max_retry: int = 5
    
    # 下载配置
    chunk: int = 1024 * 1024 * 10
    record_data: bool = False
    image_format: str = "JPEG"
    folder_mode: bool = False
    
    # 功能开关
    image_download: bool = True
    video_download: bool = True
    live_download: bool = True
    download_record: bool = True
    author_archive: bool = False
    write_mtime: bool = True
    
    # 其他配置
    language: str = "zh_CN"
    read_cookie: str = ""
    
    # 服务器配置
    server_host: str = "0.0.0.0"
    server_port: int = 5556
    log_level: str = "info"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "xhs_gui_config.json"):
        self.config_file = config_file
        self.config = XHSConfig()
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 更新配置对象
                    for key, value in data.items():
                        if hasattr(self.config, key):
                            setattr(self.config, key, value)
                print(f"✅ 配置文件加载成功: {self.config_file}")
            else:
                print(f"📝 配置文件不存在，使用默认配置: {self.config_file}")
                self.save_config()  # 保存默认配置
        except Exception as e:
            print(f"❌ 加载配置文件失败: {e}")
            print("🔄 使用默认配置")
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(self.config), f, indent=2, ensure_ascii=False)
            print(f"✅ 配置文件保存成功: {self.config_file}")
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
    
    def get_config(self) -> XHSConfig:
        """获取配置对象"""
        return self.config
    
    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
        self.save_config()
    
    def get_xhs_kwargs(self) -> Dict[str, Any]:
        """获取XHS实例化参数"""
        return {
            'work_path': self.config.work_path,
            'folder_name': self.config.folder_name,
            'name_format': self.config.name_format,
            'user_agent': self.config.user_agent,
            'cookie': self.config.cookie,
            'proxy': self.config.proxy if self.config.proxy else None,
            'timeout': self.config.timeout,
            'chunk': self.config.chunk,
            'max_retry': self.config.max_retry,
            'record_data': self.config.record_data,
            'image_format': self.config.image_format,
            'folder_mode': self.config.folder_mode,
            'image_download': self.config.image_download,
            'video_download': self.config.video_download,
            'live_download': self.config.live_download,
            'download_record': self.config.download_record,
            'language': self.config.language,
            'author_archive': self.config.author_archive,
            'write_mtime': self.config.write_mtime,
            'read_cookie': self.config.read_cookie if self.config.read_cookie else None,
        }
    
    def get_server_kwargs(self) -> Dict[str, Any]:
        """获取服务器启动参数"""
        return {
            'host': self.config.server_host,
            'port': self.config.server_port,
            'log_level': self.config.log_level,
        }
    
    def validate_config(self) -> tuple[bool, str]:
        """验证配置有效性"""
        try:
            # 检查路径
            if not self.config.work_path:
                return False, "下载路径不能为空"
            
            # 检查端口
            if not (1 <= self.config.server_port <= 65535):
                return False, "服务器端口必须在1-65535之间"
            
            # 检查超时时间
            if self.config.timeout <= 0:
                return False, "超时时间必须大于0"
            
            # 检查重试次数
            if self.config.max_retry < 0:
                return False, "重试次数不能小于0"
            
            # 检查块大小
            if self.config.chunk <= 0:
                return False, "下载块大小必须大于0"
            
            return True, "配置验证通过"
            
        except Exception as e:
            return False, f"配置验证失败: {e}"


# 全局配置管理器实例
config_manager = ConfigManager()

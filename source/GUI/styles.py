"""
样式管理器 - 扁平化设计样式
"""

# 颜色定义
COLORS = {
    # 主色调
    'primary': '#3498db',
    'primary_dark': '#2980b9',
    'primary_light': '#5dade2',
    
    # 辅助色
    'secondary': '#95a5a6',
    'secondary_dark': '#7f8c8d',
    'secondary_light': '#bdc3c7',
    
    # 状态色
    'success': '#27ae60',
    'success_dark': '#229954',
    'warning': '#f39c12',
    'warning_dark': '#e67e22',
    'danger': '#e74c3c',
    'danger_dark': '#c0392b',
    'info': '#17a2b8',
    'info_dark': '#138496',
    
    # 背景色
    'background': '#ecf0f1',
    'background_dark': '#34495e',
    'card_background': '#ffffff',
    'input_background': '#ffffff',
    
    # 文字色
    'text_primary': '#2c3e50',
    'text_secondary': '#7f8c8d',
    'text_light': '#bdc3c7',
    'text_white': '#ffffff',
    
    # 边框色
    'border': '#bdc3c7',
    'border_light': '#ecf0f1',
    'border_dark': '#95a5a6',
}

# 基础样式
BASE_STYLE = f"""
QWidget {{
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-size: 9pt;
    color: {COLORS['text_primary']};
    background-color: {COLORS['background']};
}}

QMainWindow {{
    background-color: {COLORS['background']};
}}
"""

# 按钮样式
BUTTON_STYLE = f"""
QPushButton {{
    background-color: {COLORS['primary']};
    color: {COLORS['text_white']};
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    min-height: 20px;
}}

QPushButton:hover {{
    background-color: {COLORS['primary_dark']};
}}

QPushButton:pressed {{
    background-color: {COLORS['primary_dark']};
    padding-top: 9px;
    padding-bottom: 7px;
}}

QPushButton:disabled {{
    background-color: {COLORS['secondary']};
    color: {COLORS['text_light']};
}}

QPushButton.success {{
    background-color: {COLORS['success']};
}}

QPushButton.success:hover {{
    background-color: {COLORS['success_dark']};
}}

QPushButton.warning {{
    background-color: {COLORS['warning']};
}}

QPushButton.warning:hover {{
    background-color: {COLORS['warning_dark']};
}}

QPushButton.danger {{
    background-color: {COLORS['danger']};
}}

QPushButton.danger:hover {{
    background-color: {COLORS['danger_dark']};
}}
"""

# 输入框样式
INPUT_STYLE = f"""
QLineEdit, QTextEdit, QPlainTextEdit {{
    background-color: {COLORS['input_background']};
    border: 2px solid {COLORS['border_light']};
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 9pt;
}}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
    border-color: {COLORS['primary']};
}}

QSpinBox, QDoubleSpinBox {{
    background-color: {COLORS['input_background']};
    border: 2px solid {COLORS['border_light']};
    border-radius: 6px;
    padding: 6px 8px;
    min-height: 20px;
}}

QSpinBox:focus, QDoubleSpinBox:focus {{
    border-color: {COLORS['primary']};
}}

QComboBox {{
    background-color: {COLORS['input_background']};
    border: 2px solid {COLORS['border_light']};
    border-radius: 6px;
    padding: 6px 8px;
    min-height: 20px;
}}

QComboBox:focus {{
    border-color: {COLORS['primary']};
}}

QComboBox::drop-down {{
    border: none;
    width: 20px;
}}

QComboBox::down-arrow {{
    image: none;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid {COLORS['text_secondary']};
    margin-right: 5px;
}}
"""

# 复选框和单选框样式
CHECKBOX_STYLE = f"""
QCheckBox {{
    spacing: 8px;
}}

QCheckBox::indicator {{
    width: 18px;
    height: 18px;
    border-radius: 3px;
    border: 2px solid {COLORS['border']};
    background-color: {COLORS['input_background']};
}}

QCheckBox::indicator:checked {{
    background-color: {COLORS['primary']};
    border-color: {COLORS['primary']};
    image: none;
}}

QCheckBox::indicator:checked::after {{
    content: "✓";
    color: {COLORS['text_white']};
    font-weight: bold;
}}

QRadioButton {{
    spacing: 8px;
}}

QRadioButton::indicator {{
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: 2px solid {COLORS['border']};
    background-color: {COLORS['input_background']};
}}

QRadioButton::indicator:checked {{
    background-color: {COLORS['primary']};
    border-color: {COLORS['primary']};
}}
"""

# 分组框样式
GROUPBOX_STYLE = f"""
QGroupBox {{
    font-weight: 600;
    border: 2px solid {COLORS['border_light']};
    border-radius: 8px;
    margin-top: 10px;
    padding-top: 10px;
    background-color: {COLORS['card_background']};
}}

QGroupBox::title {{
    subcontrol-origin: margin;
    left: 10px;
    padding: 0 8px 0 8px;
    color: {COLORS['text_primary']};
    background-color: {COLORS['card_background']};
}}
"""

# 标签页样式
TAB_STYLE = f"""
QTabWidget::pane {{
    border: 2px solid {COLORS['border_light']};
    border-radius: 6px;
    background-color: {COLORS['card_background']};
}}

QTabBar::tab {{
    background-color: {COLORS['secondary_light']};
    color: {COLORS['text_primary']};
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}}

QTabBar::tab:selected {{
    background-color: {COLORS['primary']};
    color: {COLORS['text_white']};
}}

QTabBar::tab:hover:!selected {{
    background-color: {COLORS['primary_light']};
}}
"""

# 表格样式
TABLE_STYLE = f"""
QTableWidget {{
    background-color: {COLORS['card_background']};
    border: 2px solid {COLORS['border_light']};
    border-radius: 6px;
    gridline-color: {COLORS['border_light']};
    selection-background-color: {COLORS['primary_light']};
}}

QTableWidget::item {{
    padding: 8px;
    border-bottom: 1px solid {COLORS['border_light']};
}}

QTableWidget::item:selected {{
    background-color: {COLORS['primary_light']};
    color: {COLORS['text_primary']};
}}

QHeaderView::section {{
    background-color: {COLORS['secondary_light']};
    color: {COLORS['text_primary']};
    padding: 8px;
    border: none;
    border-bottom: 2px solid {COLORS['border']};
    font-weight: 600;
}}
"""

# 滚动条样式
SCROLLBAR_STYLE = f"""
QScrollBar:vertical {{
    background-color: {COLORS['background']};
    width: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:vertical {{
    background-color: {COLORS['secondary']};
    border-radius: 6px;
    min-height: 20px;
}}

QScrollBar::handle:vertical:hover {{
    background-color: {COLORS['secondary_dark']};
}}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
    height: 0px;
}}

QScrollBar:horizontal {{
    background-color: {COLORS['background']};
    height: 12px;
    border-radius: 6px;
}}

QScrollBar::handle:horizontal {{
    background-color: {COLORS['secondary']};
    border-radius: 6px;
    min-width: 20px;
}}

QScrollBar::handle:horizontal:hover {{
    background-color: {COLORS['secondary_dark']};
}}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
    width: 0px;
}}
"""

# 状态栏样式
STATUSBAR_STYLE = f"""
QStatusBar {{
    background-color: {COLORS['card_background']};
    border-top: 1px solid {COLORS['border_light']};
    color: {COLORS['text_secondary']};
    padding: 4px;
}}
"""

# 组合所有样式
COMPLETE_STYLE = f"""
{BASE_STYLE}
{BUTTON_STYLE}
{INPUT_STYLE}
{CHECKBOX_STYLE}
{GROUPBOX_STYLE}
{TAB_STYLE}
{TABLE_STYLE}
{SCROLLBAR_STYLE}
{STATUSBAR_STYLE}
"""

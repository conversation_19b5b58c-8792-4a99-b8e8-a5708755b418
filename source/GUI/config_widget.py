"""
配置界面组件
"""

import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QLineEdit, QSpinBox, QCheckBox,
    QComboBox, QPushButton, QFileDialog, QMessageBox,
    QTabWidget, QScrollArea
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIntValidator

from .config_manager import config_manager, XHSConfig


class ConfigWidget(QWidget):
    """配置界面组件"""
    
    config_changed = Signal()  # 配置改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = config_manager.get_config()
        self.init_ui()
        self.load_config_values()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 基本配置标签页
        basic_tab = self.create_basic_config_tab()
        tab_widget.addTab(basic_tab, "基本配置")
        
        # 网络配置标签页
        network_tab = self.create_network_config_tab()
        tab_widget.addTab(network_tab, "网络配置")
        
        # 下载配置标签页
        download_tab = self.create_download_config_tab()
        tab_widget.addTab(download_tab, "下载配置")
        
        # 服务器配置标签页
        server_tab = self.create_server_config_tab()
        tab_widget.addTab(server_tab, "服务器配置")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.reset_button = QPushButton("重置默认")
        self.reset_button.clicked.connect(self.reset_to_defaults)
        button_layout.addWidget(self.reset_button)
        
        self.save_button = QPushButton("保存配置")
        self.save_button.setProperty("class", "success")
        self.save_button.clicked.connect(self.save_config)
        button_layout.addWidget(self.save_button)
        
        layout.addLayout(button_layout)
    
    def create_basic_config_tab(self):
        """创建基本配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 路径配置组
        path_group = QGroupBox("路径配置")
        path_layout = QGridLayout(path_group)
        
        # 下载路径
        path_layout.addWidget(QLabel("下载路径:"), 0, 0)
        self.work_path_edit = QLineEdit()
        path_layout.addWidget(self.work_path_edit, 0, 1)
        
        browse_button = QPushButton("浏览")
        browse_button.clicked.connect(self.browse_work_path)
        path_layout.addWidget(browse_button, 0, 2)
        
        # 文件夹名称
        path_layout.addWidget(QLabel("文件夹名称:"), 1, 0)
        self.folder_name_edit = QLineEdit()
        path_layout.addWidget(self.folder_name_edit, 1, 1, 1, 2)
        
        # 文件命名格式
        path_layout.addWidget(QLabel("命名格式:"), 2, 0)
        self.name_format_combo = QComboBox()
        self.name_format_combo.addItems([
            "作品ID", "发布时间", "作者昵称", "作品标题",
            "发布时间 作者昵称", "发布时间 作品标题",
            "作者昵称 作品标题", "发布时间 作者昵称 作品标题"
        ])
        self.name_format_combo.setEditable(True)
        path_layout.addWidget(self.name_format_combo, 2, 1, 1, 2)
        
        layout.addWidget(path_group)
        
        # 功能开关组
        function_group = QGroupBox("功能开关")
        function_layout = QGridLayout(function_group)
        
        self.image_download_cb = QCheckBox("下载图片")
        function_layout.addWidget(self.image_download_cb, 0, 0)
        
        self.video_download_cb = QCheckBox("下载视频")
        function_layout.addWidget(self.video_download_cb, 0, 1)
        
        self.live_download_cb = QCheckBox("下载动图")
        function_layout.addWidget(self.live_download_cb, 1, 0)
        
        self.download_record_cb = QCheckBox("记录下载历史")
        function_layout.addWidget(self.download_record_cb, 1, 1)
        
        self.folder_mode_cb = QCheckBox("单独文件夹")
        function_layout.addWidget(self.folder_mode_cb, 2, 0)
        
        self.author_archive_cb = QCheckBox("按作者分类")
        function_layout.addWidget(self.author_archive_cb, 2, 1)
        
        self.write_mtime_cb = QCheckBox("设置文件时间")
        function_layout.addWidget(self.write_mtime_cb, 3, 0)
        
        self.record_data_cb = QCheckBox("保存数据文件")
        function_layout.addWidget(self.record_data_cb, 3, 1)
        
        layout.addWidget(function_group)
        
        # 其他配置组
        other_group = QGroupBox("其他配置")
        other_layout = QGridLayout(other_group)
        
        # 图片格式
        other_layout.addWidget(QLabel("图片格式:"), 0, 0)
        self.image_format_combo = QComboBox()
        self.image_format_combo.addItems(["JPEG", "PNG", "WEBP", "AUTO", "HEIC"])
        other_layout.addWidget(self.image_format_combo, 0, 1)
        
        # 语言设置
        other_layout.addWidget(QLabel("语言:"), 1, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems(["zh_CN", "en_US"])
        other_layout.addWidget(self.language_combo, 1, 1)
        
        layout.addWidget(other_group)
        layout.addStretch()
        
        return widget
    
    def create_network_config_tab(self):
        """创建网络配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 网络设置组
        network_group = QGroupBox("网络设置")
        network_layout = QGridLayout(network_group)
        
        # User-Agent
        network_layout.addWidget(QLabel("User-Agent:"), 0, 0)
        self.user_agent_edit = QLineEdit()
        self.user_agent_edit.setPlaceholderText("留空使用默认值")
        network_layout.addWidget(self.user_agent_edit, 0, 1)
        
        # Cookie
        network_layout.addWidget(QLabel("Cookie:"), 1, 0)
        self.cookie_edit = QLineEdit()
        self.cookie_edit.setPlaceholderText("小红书网页版Cookie（可选）")
        network_layout.addWidget(self.cookie_edit, 1, 1)
        
        # 代理设置
        network_layout.addWidget(QLabel("代理:"), 2, 0)
        self.proxy_edit = QLineEdit()
        self.proxy_edit.setPlaceholderText("例如: http://127.0.0.1:7890")
        network_layout.addWidget(self.proxy_edit, 2, 1)
        
        # 浏览器Cookie
        network_layout.addWidget(QLabel("浏览器Cookie:"), 3, 0)
        self.read_cookie_combo = QComboBox()
        self.read_cookie_combo.addItems(["不读取", "Chrome", "Firefox", "Edge", "Safari"])
        network_layout.addWidget(self.read_cookie_combo, 3, 1)
        
        layout.addWidget(network_group)
        
        # 请求设置组
        request_group = QGroupBox("请求设置")
        request_layout = QGridLayout(request_group)
        
        # 超时时间
        request_layout.addWidget(QLabel("超时时间(秒):"), 0, 0)
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(1, 300)
        self.timeout_spin.setSuffix(" 秒")
        request_layout.addWidget(self.timeout_spin, 0, 1)
        
        # 重试次数
        request_layout.addWidget(QLabel("重试次数:"), 1, 0)
        self.max_retry_spin = QSpinBox()
        self.max_retry_spin.setRange(0, 20)
        self.max_retry_spin.setSuffix(" 次")
        request_layout.addWidget(self.max_retry_spin, 1, 1)
        
        layout.addWidget(request_group)
        layout.addStretch()

        return widget

    def create_download_config_tab(self):
        """创建下载配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 下载设置组
        download_group = QGroupBox("下载设置")
        download_layout = QGridLayout(download_group)

        # 下载块大小
        download_layout.addWidget(QLabel("下载块大小:"), 0, 0)
        self.chunk_combo = QComboBox()
        self.chunk_combo.addItems([
            "1 MB", "5 MB", "10 MB", "20 MB", "50 MB"
        ])
        self.chunk_combo.setEditable(True)
        download_layout.addWidget(self.chunk_combo, 0, 1)

        layout.addWidget(download_group)
        layout.addStretch()

        return widget

    def create_server_config_tab(self):
        """创建服务器配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 服务器设置组
        server_group = QGroupBox("服务器设置")
        server_layout = QGridLayout(server_group)

        # 服务器地址
        server_layout.addWidget(QLabel("服务器地址:"), 0, 0)
        self.server_host_edit = QLineEdit()
        server_layout.addWidget(self.server_host_edit, 0, 1)

        # 服务器端口
        server_layout.addWidget(QLabel("服务器端口:"), 1, 0)
        self.server_port_spin = QSpinBox()
        self.server_port_spin.setRange(1, 65535)
        server_layout.addWidget(self.server_port_spin, 1, 1)

        # 日志级别
        server_layout.addWidget(QLabel("日志级别:"), 2, 0)
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["debug", "info", "warning", "error"])
        server_layout.addWidget(self.log_level_combo, 2, 1)

        layout.addWidget(server_group)
        layout.addStretch()

        return widget

    def browse_work_path(self):
        """浏览下载路径"""
        path = QFileDialog.getExistingDirectory(
            self, "选择下载路径", self.work_path_edit.text()
        )
        if path:
            self.work_path_edit.setText(path)

    def load_config_values(self):
        """加载配置值到界面"""
        # 基本配置
        self.work_path_edit.setText(self.config.work_path)
        self.folder_name_edit.setText(self.config.folder_name)
        self.name_format_combo.setCurrentText(self.config.name_format)

        # 功能开关
        self.image_download_cb.setChecked(self.config.image_download)
        self.video_download_cb.setChecked(self.config.video_download)
        self.live_download_cb.setChecked(self.config.live_download)
        self.download_record_cb.setChecked(self.config.download_record)
        self.folder_mode_cb.setChecked(self.config.folder_mode)
        self.author_archive_cb.setChecked(self.config.author_archive)
        self.write_mtime_cb.setChecked(self.config.write_mtime)
        self.record_data_cb.setChecked(self.config.record_data)

        # 其他配置
        self.image_format_combo.setCurrentText(self.config.image_format)
        self.language_combo.setCurrentText(self.config.language)

        # 网络配置
        self.user_agent_edit.setText(self.config.user_agent)
        self.cookie_edit.setText(self.config.cookie)
        self.proxy_edit.setText(self.config.proxy)
        self.timeout_spin.setValue(self.config.timeout)
        self.max_retry_spin.setValue(self.config.max_retry)

        # 浏览器Cookie
        read_cookie_map = {
            "": "不读取",
            "chrome": "Chrome",
            "firefox": "Firefox",
            "edge": "Edge",
            "safari": "Safari"
        }
        self.read_cookie_combo.setCurrentText(
            read_cookie_map.get(self.config.read_cookie.lower(), "不读取")
        )

        # 下载配置
        chunk_mb = self.config.chunk // (1024 * 1024)
        self.chunk_combo.setCurrentText(f"{chunk_mb} MB")

        # 服务器配置
        self.server_host_edit.setText(self.config.server_host)
        self.server_port_spin.setValue(self.config.server_port)
        self.log_level_combo.setCurrentText(self.config.log_level)

    def save_config(self):
        """保存配置"""
        try:
            # 收集界面数据
            config_data = self.collect_config_data()

            # 验证配置
            if not self.validate_config_data(config_data):
                return

            # 更新配置
            config_manager.update_config(**config_data)

            # 发送配置改变信号
            self.config_changed.emit()

            QMessageBox.information(self, "成功", "配置保存成功！")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败：{e}")

    def collect_config_data(self):
        """收集界面配置数据"""
        # 浏览器Cookie映射
        read_cookie_map = {
            "不读取": "",
            "Chrome": "chrome",
            "Firefox": "firefox",
            "Edge": "edge",
            "Safari": "safari"
        }

        # 解析下载块大小
        chunk_text = self.chunk_combo.currentText()
        try:
            chunk_mb = int(chunk_text.split()[0])
            chunk = chunk_mb * 1024 * 1024
        except:
            chunk = 10 * 1024 * 1024  # 默认10MB

        return {
            # 基本配置
            'work_path': self.work_path_edit.text().strip(),
            'folder_name': self.folder_name_edit.text().strip(),
            'name_format': self.name_format_combo.currentText().strip(),

            # 功能开关
            'image_download': self.image_download_cb.isChecked(),
            'video_download': self.video_download_cb.isChecked(),
            'live_download': self.live_download_cb.isChecked(),
            'download_record': self.download_record_cb.isChecked(),
            'folder_mode': self.folder_mode_cb.isChecked(),
            'author_archive': self.author_archive_cb.isChecked(),
            'write_mtime': self.write_mtime_cb.isChecked(),
            'record_data': self.record_data_cb.isChecked(),

            # 其他配置
            'image_format': self.image_format_combo.currentText(),
            'language': self.language_combo.currentText(),

            # 网络配置
            'user_agent': self.user_agent_edit.text().strip(),
            'cookie': self.cookie_edit.text().strip(),
            'proxy': self.proxy_edit.text().strip(),
            'timeout': self.timeout_spin.value(),
            'max_retry': self.max_retry_spin.value(),
            'read_cookie': read_cookie_map.get(self.read_cookie_combo.currentText(), ""),

            # 下载配置
            'chunk': chunk,

            # 服务器配置
            'server_host': self.server_host_edit.text().strip(),
            'server_port': self.server_port_spin.value(),
            'log_level': self.log_level_combo.currentText(),
        }

    def validate_config_data(self, config_data):
        """验证配置数据"""
        if not config_data['work_path']:
            QMessageBox.warning(self, "警告", "下载路径不能为空！")
            return False

        if not config_data['folder_name']:
            QMessageBox.warning(self, "警告", "文件夹名称不能为空！")
            return False

        if not config_data['name_format']:
            QMessageBox.warning(self, "警告", "命名格式不能为空！")
            return False

        return True

    def reset_to_defaults(self):
        """重置为默认配置"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置为默认配置吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 创建默认配置
            default_config = XHSConfig()

            # 更新配置管理器
            config_manager.config = default_config
            config_manager.save_config()

            # 重新加载界面
            self.config = default_config
            self.load_config_values()

            # 发送配置改变信号
            self.config_changed.emit()

            QMessageBox.information(self, "成功", "已重置为默认配置！")

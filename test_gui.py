#!/usr/bin/env python3
"""
测试GUI界面
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def test_imports():
    """测试导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试PySide6
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6 导入成功")
        
        # 测试qasync
        import qasync
        print("✅ qasync 导入成功")
        
        # 测试项目模块
        from source.GUI.config_manager import config_manager
        print("✅ 配置管理器导入成功")
        
        from source.GUI.styles import COMPLETE_STYLE
        print("✅ 样式模块导入成功")
        
        from source.GUI.config_widget import ConfigWidget
        print("✅ 配置组件导入成功")
        
        from source.GUI.task_widget import TaskWidget
        print("✅ 任务组件导入成功")
        
        from source.GUI.main_window import MainWindow
        print("✅ 主窗口导入成功")
        
        from source.GUI.app import XHSGuiApplication
        print("✅ GUI应用程序导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_config():
    """测试配置"""
    print("\n🧪 测试配置管理...")
    
    try:
        from source.GUI.config_manager import config_manager
        
        # 测试配置加载
        config = config_manager.get_config()
        print(f"✅ 配置加载成功: {config.work_path}")
        
        # 测试配置验证
        is_valid, message = config_manager.validate_config()
        print(f"✅ 配置验证: {is_valid} - {message}")
        
        # 测试XHS参数
        xhs_kwargs = config_manager.get_xhs_kwargs()
        print(f"✅ XHS参数: {len(xhs_kwargs)} 个参数")
        
        # 测试服务器参数
        server_kwargs = config_manager.get_server_kwargs()
        print(f"✅ 服务器参数: {server_kwargs}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


def test_gui_creation():
    """测试GUI创建"""
    print("\n🧪 测试GUI组件创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from source.GUI.config_widget import ConfigWidget
        from source.GUI.task_widget import TaskWidget
        
        # 创建应用程序
        app = QApplication([])
        
        # 测试配置组件
        config_widget = ConfigWidget()
        print("✅ 配置组件创建成功")
        
        # 测试任务组件
        task_widget = TaskWidget()
        print("✅ 任务组件创建成功")
        
        # 清理
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 XHS-Downloader GUI 测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖项")
        return 1
    
    # 测试配置
    if not test_config():
        print("\n❌ 配置测试失败")
        return 1
    
    # 测试GUI创建
    if not test_gui_creation():
        print("\n❌ GUI创建测试失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！")
    print("\n现在可以运行以下命令启动GUI:")
    print("python gui_launcher.py")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

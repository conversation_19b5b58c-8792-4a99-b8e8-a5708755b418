"""
批量下载小红书内容的示例代码
演示如何使用新的批量API接口
"""

import asyncio
import httpx
import json
from typing import List


async def submit_batch_task(urls: List[str], download: bool = True) -> str:
    """提交批量下载任务"""
    
    # API服务器地址
    base_url = "http://localhost:5556"
    
    # 准备请求数据
    payload = {
        "urls": urls,
        "download": download,
        "index": None,
        "cookie": None,
        "proxy": None,
        "skip": False
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                f"{base_url}/xhs/batch/",
                json=payload,
                timeout=30.0
            )
            response.raise_for_status()
            
            result = response.json()
            print(f"✅ 批量任务提交成功!")
            print(f"任务ID: {result['task_id']}")
            print(f"状态: {result['status']}")
            print(f"总链接数: {result['total_urls']}")
            print(f"消息: {result['message']}")
            
            return result['task_id']
            
        except httpx.HTTPError as e:
            print(f"❌ 提交批量任务失败: {e}")
            return ""


async def check_task_status(task_id: str):
    """检查任务状态"""
    
    base_url = "http://localhost:5556"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/xhs/batch/{task_id}",
                timeout=10.0
            )
            response.raise_for_status()
            
            result = response.json()
            print(f"\n📊 任务状态更新:")
            print(f"任务ID: {result['task_id']}")
            print(f"状态: {result['status']}")
            print(f"总链接数: {result['total_urls']}")
            print(f"已处理: {result['processed_urls']}")
            print(f"失败数: {result['failed_urls']}")
            print(f"创建时间: {result['created_at']}")
            print(f"更新时间: {result['updated_at']}")
            
            if result['errors']:
                print(f"错误信息: {result['errors']}")
            
            return result
            
        except httpx.HTTPError as e:
            print(f"❌ 获取任务状态失败: {e}")
            return None


async def get_all_tasks():
    """获取所有任务状态"""
    
    base_url = "http://localhost:5556"
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(
                f"{base_url}/xhs/batch/",
                timeout=10.0
            )
            response.raise_for_status()
            
            tasks = response.json()
            print(f"\n📋 所有任务列表 (共{len(tasks)}个):")
            
            for task in tasks:
                print(f"  - 任务ID: {task['task_id'][:8]}...")
                print(f"    状态: {task['status']}")
                print(f"    进度: {task['processed_urls']}/{task['total_urls']}")
                print(f"    创建时间: {task['created_at']}")
                print()
            
            return tasks
            
        except httpx.HTTPError as e:
            print(f"❌ 获取任务列表失败: {e}")
            return []


async def monitor_task_progress(task_id: str, check_interval: int = 5):
    """监控任务进度直到完成"""
    
    print(f"🔄 开始监控任务 {task_id[:8]}... 的进度")
    
    while True:
        task_info = await check_task_status(task_id)
        
        if not task_info:
            print("❌ 无法获取任务状态，停止监控")
            break
        
        status = task_info['status']
        
        if status in ['completed', 'failed']:
            print(f"✅ 任务已完成，最终状态: {status}")
            
            # 显示详细结果
            if task_info.get('results'):
                print(f"\n📄 处理结果详情:")
                for i, result in enumerate(task_info['results'], 1):
                    print(f"  {i}. URL: {result['url']}")
                    print(f"     状态: {result['status']}")
                    if result['status'] == 'failed':
                        print(f"     错误: {result.get('error', '未知错误')}")
                    print()
            
            break
        
        print(f"⏳ 任务进行中，{check_interval}秒后再次检查...")
        await asyncio.sleep(check_interval)


async def main():
    """主函数示例"""
    
    # 示例链接列表
    example_urls = [
        "https://www.xiaohongshu.com/explore/6848e611000000002202d6d3?xsec_token=ABRhUX7jj7zYM6D5T9g39y4GBcKWKfQot5p3410L2Ix4E=&xsec_source=pc_feed",
        "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c8",
        "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c9",
        # 添加更多链接...
    ]
    
    print("🚀 批量下载小红书内容示例")
    print("=" * 50)
    
    # 1. 提交批量任务
    task_id = await submit_batch_task(example_urls, download=True)
    
    if not task_id:
        print("❌ 任务提交失败，退出")
        return
    
    # 2. 监控任务进度
    await monitor_task_progress(task_id, check_interval=3)
    
    # 3. 获取所有任务状态
    await get_all_tasks()


if __name__ == "__main__":
    print("请确保XHS-Downloader服务器正在运行 (python -m source.CLI server)")
    print("服务器地址: http://localhost:5556")
    print()
    
    # 运行示例
    asyncio.run(main())

#!/usr/bin/env python3
"""
测试GUI修改的脚本
验证任务表格不可编辑和打开文件夹功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    try:
        print("🔍 测试导入...")
        
        # 测试任务组件导入
        from source.GUI.task_widget import TaskWidget
        print("✅ TaskWidget 导入成功")
        
        # 测试主窗口导入
        from source.GUI.main_window import MainWindow
        print("✅ MainWindow 导入成功")
        
        # 测试配置管理器导入
        from source.GUI.config_manager import config_manager
        print("✅ config_manager 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_task_widget_properties():
    """测试任务组件属性"""
    try:
        print("\n🔍 测试TaskWidget属性...")
        
        from PySide6.QtWidgets import QApplication, QTableWidget
        from source.GUI.task_widget import TaskWidget
        
        # 创建应用程序实例（如果不存在）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建TaskWidget实例
        task_widget = TaskWidget()
        
        # 检查表格是否不可编辑
        edit_triggers = task_widget.task_table.editTriggers()
        if edit_triggers == QTableWidget.EditTrigger.NoEditTriggers:
            print("✅ 任务表格已设置为不可编辑")
        else:
            print(f"❌ 任务表格编辑设置错误: {edit_triggers}")
            return False
        
        # 检查是否有打开文件夹按钮
        if hasattr(task_widget, 'open_folder_button'):
            print("✅ 任务组件包含打开文件夹按钮")
        else:
            print("❌ 任务组件缺少打开文件夹按钮")
            return False
        
        # 检查是否有打开文件夹方法
        if hasattr(task_widget, 'open_download_folder'):
            print("✅ 任务组件包含打开文件夹方法")
        else:
            print("❌ 任务组件缺少打开文件夹方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试TaskWidget属性失败: {e}")
        return False

def test_main_window_properties():
    """测试主窗口属性"""
    try:
        print("\n🔍 测试MainWindow属性...")
        
        from PySide6.QtWidgets import QApplication
        from source.GUI.main_window import MainWindow
        
        # 创建应用程序实例（如果不存在）
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建MainWindow实例
        main_window = MainWindow()
        
        # 检查是否有打开文件夹按钮
        if hasattr(main_window, 'open_folder_button'):
            print("✅ 主窗口包含打开文件夹按钮")
        else:
            print("❌ 主窗口缺少打开文件夹按钮")
            return False
        
        # 检查是否有打开文件夹方法
        if hasattr(main_window, 'open_download_folder'):
            print("✅ 主窗口包含打开文件夹方法")
        else:
            print("❌ 主窗口缺少打开文件夹方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试MainWindow属性失败: {e}")
        return False

def test_config_access():
    """测试配置访问"""
    try:
        print("\n🔍 测试配置访问...")
        
        from source.GUI.config_manager import config_manager
        
        # 获取配置
        config = config_manager.get_config()
        
        # 检查关键配置项
        if hasattr(config, 'work_path') and hasattr(config, 'folder_name'):
            print(f"✅ 配置访问正常")
            print(f"   下载路径: {config.work_path}")
            print(f"   文件夹名: {config.folder_name}")
            
            # 构建完整路径
            full_path = os.path.join(config.work_path, config.folder_name)
            print(f"   完整路径: {full_path}")
            
            return True
        else:
            print("❌ 配置项缺失")
            return False
        
    except Exception as e:
        print(f"❌ 测试配置访问失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试GUI修改...")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        test_imports,
        test_config_access,
        test_task_widget_properties,
        test_main_window_properties,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI修改成功！")
        print("\n✨ 修改内容:")
        print("1. ✅ 任务管理表格已设置为不可编辑")
        print("2. ✅ 任务管理界面新增了'打开下载文件夹'按钮")
        print("3. ✅ 主窗口控制面板新增了'打开下载文件夹'按钮")
        print("4. ✅ 支持跨平台打开文件夹（Windows/macOS/Linux）")
        print("5. ✅ 自动创建不存在的下载文件夹")
        return True
    else:
        print("❌ 部分测试失败，请检查修改")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

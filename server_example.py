"""
启动XHS-Downloader API服务器的示例代码
包含批量下载功能
"""

import asyncio
from source import XHS


async def start_server():
    """启动API服务器"""
    
    # 配置参数
    work_path = "D:/Download"  # 下载文件保存路径
    folder_name = "XHS_Batch_Downloads"  # 文件夹名称
    name_format = "作品ID"  # 文件命名格式
    user_agent = ""  # User-Agent
    cookie = ""  # 小红书Cookie（可选）
    proxy = None  # 代理设置
    timeout = 10  # 请求超时时间
    chunk = 1024 * 1024 * 10  # 下载块大小
    max_retry = 5  # 最大重试次数
    record_data = False  # 是否保存数据到文件
    image_format = "JPEG"  # 图片格式
    folder_mode = False  # 是否为每个作品创建单独文件夹
    image_download = True  # 是否下载图片
    video_download = True  # 是否下载视频
    live_download = True  # 是否下载动图
    download_record = True  # 是否记录下载历史
    language = "zh_CN"  # 语言设置
    author_archive = False  # 是否按作者分类存储
    write_mtime = True  # 是否设置文件修改时间
    read_cookie = None  # 是否读取浏览器Cookie
    
    # 创建XHS实例
    async with XHS(
        work_path=work_path,
        folder_name=folder_name,
        name_format=name_format,
        user_agent=user_agent,
        cookie=cookie,
        proxy=proxy,
        timeout=timeout,
        chunk=chunk,
        max_retry=max_retry,
        record_data=record_data,
        image_format=image_format,
        folder_mode=folder_mode,
        image_download=image_download,
        video_download=video_download,
        live_download=live_download,
        download_record=download_record,
        language=language,
        read_cookie=read_cookie,
        author_archive=author_archive,
        write_mtime=write_mtime,
    ) as xhs:
        
        print("🚀 启动XHS-Downloader API服务器...")
        print("=" * 50)
        print(f"📁 下载路径: {work_path}")
        print(f"📂 文件夹名称: {folder_name}")
        print(f"🌐 服务器地址: http://0.0.0.0:5556")
        print("=" * 50)
        print()
        print("📋 可用的API端点:")
        print("  GET  /                    - 重定向到项目主页")
        print("  POST /xhs/               - 单个链接处理")
        print("  POST /xhs/batch/         - 批量链接处理")
        print("  GET  /xhs/batch/{task_id} - 获取任务状态")
        print("  GET  /xhs/batch/         - 获取所有任务")
        print()
        print("💡 使用示例:")
        print("  python batch_example.py  - 运行批量下载示例")
        print()
        print("⚠️  按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        try:
            # 启动服务器
            await xhs.run_server(
                host="0.0.0.0",
                port=5556,
                log_level="info"
            )
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")
        except Exception as e:
            print(f"❌ 服务器启动失败: {e}")


if __name__ == "__main__":
    # 运行服务器
    asyncio.run(start_server())

# 文本数据保存功能实现说明

## 功能概述

根据用户要求，参考原始 `extract` 方法中的文本数据保存代码，在界面启动服务中实现了文本数据保存功能。现在当通过GUI界面或API调用处理小红书作品时，系统会自动将提取的文本数据保存为JSON格式的文件。

## 实现详情

### 1. 批量处理服务 (source/application/batch_service.py)

#### 新增功能：
- **新增方法**: `_save_text_data(self, result: dict, xhs_instance)`
  - 负责将提取的作品数据保存为JSON文件
  - **智能文件夹命名**：使用与媒体文件相同的命名规则，确保文本文件和媒体文件保存在同一文件夹
  - 使用UTF-8编码，JSON格式化输出

#### 修改内容：
1. **导入模块**：添加了 `json` 和 `os` 模块导入
2. **处理逻辑**：在 `_process_batch_task` 方法中，当成功提取作品数据后，自动调用文本保存功能
3. **命名规则修复**：使用 `xhs_instance._XHS__naming_rules(result)` 生成与媒体文件一致的文件夹名称

<augment_code_snippet path="source/application/batch_service.py" mode="EXCERPT">
````python
def _save_text_data(self, result: dict, xhs_instance):
    """保存文本数据到文件"""
    try:
        if not result or '作品ID' not in result:
            return

        # 使用与下载文件相同的命名规则
        folder_name = xhs_instance._XHS__naming_rules(result)

        # 构建文件路径，使用与媒体文件相同的文件夹
        file_path = str(xhs_instance.manager.folder.joinpath(folder_name)) + os.sep + "text.txt"
````
</augment_code_snippet>

### 2. API处理服务 (source/application/app.py)

#### 新增功能：
- **新增函数**: `_save_text_data_for_api(result: dict, xhs_instance)`
  - 为单个API调用提供文本数据保存功能
  - **智能文件夹命名**：同样使用与媒体文件相同的命名规则
  - 与批量处理使用相同的保存逻辑和格式

#### 修改内容：
1. 在全局API处理函数 `handle` 中，当成功提取作品数据后，自动调用文本保存功能
2. **命名规则修复**：使用 `xhs_instance._XHS__naming_rules(result)` 确保文件夹名称一致性

<augment_code_snippet path="source/application/app.py" mode="EXCERPT">
````python
if data := await xhs._XHS__deal_extract(...):
    # 保存文本数据到文件
    _save_text_data_for_api(data, xhs)
    msg = _("获取小红书作品数据成功")
````
</augment_code_snippet>

## 技术特点

### 1. 智能文件夹命名（重要修复）
- **问题解决**：修复了文本文件与媒体文件保存在不同文件夹的问题
- **命名一致性**：使用 `xhs_instance._XHS__naming_rules(result)` 方法，确保文本文件与媒体文件使用相同的文件夹命名规则
- **支持所有命名方式**：无论界面选择"作品ID"、"作品标题"、"发布时间 作者昵称 作品标题"等任何命名方式，文本文件都会保存到正确的位置

### 2. 与原始代码保持一致
- 保持相同的JSON格式化参数：`ensure_ascii=False, indent=4`
- 使用UTF-8编码确保中文字符正确保存
- 文件名固定为 `text.txt`

### 3. 跨平台兼容性
- 使用 `os.sep` 替代硬编码的路径分隔符
- 使用 `os.makedirs` 确保目录创建的跨平台兼容性

### 4. 错误处理
- 完整的异常捕获和日志记录
- 数据验证：检查结果是否包含必要的 `作品ID` 字段
- 自动创建必要的目录结构

### 5. 集成方式
- **批量处理**：集成到 `BatchProcessingService` 的任务处理流程中
- **单个API**：集成到全局API处理函数中
- **无侵入性**：不影响现有的下载和数据处理逻辑

## 文件结构

保存的文本数据文件结构会根据界面选择的命名方式动态调整：

### 当命名方式为"作品ID"时：
```
下载根目录/
├── 64a1b2c3d4e5f6789/
│   ├── text.txt          # JSON格式的作品信息
│   ├── [图片文件...]     # 媒体文件
│   └── [视频文件...]     # 媒体文件
└── ...
```

### 当命名方式为"作品标题"时：
```
下载根目录/
├── 美丽的风景照片/
│   ├── text.txt          # JSON格式的作品信息
│   ├── [图片文件...]     # 媒体文件
│   └── [视频文件...]     # 媒体文件
└── ...
```

### 当命名方式为"发布时间 作者昵称 作品标题"时：
```
下载根目录/
├── 2024.01.15_摄影师小王_美丽的风景照片/
│   ├── text.txt          # JSON格式的作品信息
│   ├── [图片文件...]     # 媒体文件
│   └── [视频文件...]     # 媒体文件
└── ...
```

**关键特点**：无论选择哪种命名方式，`text.txt` 文件都会与对应的媒体文件保存在同一个文件夹中。

## JSON文件内容示例

```json
{
    "作品ID": "64a1b2c3d4e5f6789",
    "作品标题": "美丽的风景照片",
    "作者昵称": "摄影师小王",
    "作者ID": "user123456",
    "作品描述": "今天拍摄的美丽风景，希望大家喜欢",
    "作品类型": "图文",
    "发布时间": "2024-01-15 14:30:00",
    "点赞数量": 1250,
    "评论数量": 89,
    "收藏数量": 156,
    "分享数量": 23,
    "作品标签": ["风景", "摄影", "自然"],
    "采集时间": "2024-01-16 10:15:30"
}
```

## 使用场景

1. **批量下载**：通过GUI界面批量处理多个URL时，每个成功处理的作品都会自动保存文本数据
2. **单个API调用**：通过API接口处理单个作品时，也会自动保存文本数据
3. **数据备份**：为下载的媒体文件提供完整的元数据备份
4. **数据分析**：便于后续对作品信息进行分析和处理

## 日志记录

- **成功保存**：记录到应用日志，包含作品ID信息
- **保存失败**：记录错误信息到日志，便于问题排查
- **数据验证**：当数据不完整时，会跳过保存并记录相应信息

## 问题修复说明

### 修复的问题
**原问题**：保存文本时使用固定的作品ID作为文件夹名称，当界面选中命名方式为其它时，会导致文本没有正确存储到对应的文件夹。

### 修复方案
1. **根本原因分析**：原实现使用 `result['作品ID']` 作为文件夹名称，但媒体文件下载使用的是 `__naming_rules` 方法生成的名称
2. **解决方案**：调用 `xhs_instance._XHS__naming_rules(result)` 方法，使用与媒体文件完全相同的命名规则
3. **修复位置**：
   - `source/application/batch_service.py` 中的 `_save_text_data` 方法
   - `source/application/app.py` 中的 `_save_text_data_for_api` 函数

### 修复效果
- ✅ 文本文件与媒体文件现在保存在同一个文件夹中
- ✅ 支持所有命名方式：作品ID、作品标题、发布时间+作者昵称+作品标题等
- ✅ 自动适应用户在界面中选择的任何命名规则
- ✅ 保持与原始下载逻辑的完全一致性

## 总结

此次实现完全按照用户要求，参考了原始 `extract` 方法中的文本数据保存代码，在界面启动服务中成功集成了文本数据保存功能。通过修复命名规则问题，确保了文本文件与媒体文件的存储位置完全一致。无论是通过GUI界面的批量处理，还是通过API的单个调用，都能自动将提取的作品信息保存为结构化的JSON文件到正确的位置，为用户提供完整的数据备份和后续分析支持。

#!/usr/bin/env python3
"""
测试文本数据保存功能的脚本
验证在界面启动服务中，文本数据是否正确保存到对应位置
"""

import sys
import os
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_batch_service_text_save():
    """测试批量服务的文本保存功能"""
    try:
        print("🔍 测试批量服务文本保存功能...")
        
        # 导入批量服务
        from source.application.batch_service import BatchProcessingService
        
        # 创建批量服务实例
        batch_service = BatchProcessingService()
        
        # 检查是否有保存文本数据的方法
        if hasattr(batch_service, '_save_text_data'):
            print("✅ 批量服务包含 _save_text_data 方法")
        else:
            print("❌ 批量服务缺少 _save_text_data 方法")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试批量服务文本保存功能失败: {e}")
        return False

def test_api_text_save():
    """测试API文本保存功能"""
    try:
        print("\n🔍 测试API文本保存功能...")
        
        # 导入app模块
        from source.application import app
        
        # 检查是否有保存文本数据的函数
        if hasattr(app, '_save_text_data_for_api'):
            print("✅ API模块包含 _save_text_data_for_api 函数")
        else:
            print("❌ API模块缺少 _save_text_data_for_api 函数")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试API文本保存功能失败: {e}")
        return False

def test_imports():
    """测试必要的导入"""
    try:
        print("\n🔍 测试必要的导入...")
        
        # 测试批量服务导入
        from source.application.batch_service import BatchProcessingService
        print("✅ BatchProcessingService 导入成功")
        
        # 测试app模块导入
        from source.application import app
        print("✅ app 模块导入成功")
        
        # 检查json导入
        import json
        print("✅ json 模块导入成功")
        
        # 检查os导入
        import os
        print("✅ os 模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_mock_text_save():
    """模拟测试文本保存功能"""
    try:
        print("\n🔍 模拟测试文本保存功能...")
        
        # 创建模拟数据
        mock_result = {
            '作品ID': 'test_work_id_12345',
            '作品标题': '测试作品标题',
            '作者昵称': '测试作者',
            '作品描述': '这是一个测试作品描述',
            '发布时间': '2024-01-01 12:00:00',
            '点赞数量': 100,
            '评论数量': 50,
            '收藏数量': 25,
            '分享数量': 10
        }
        
        # 创建临时测试目录
        test_dir = Path("./test_output")
        test_dir.mkdir(exist_ok=True)
        
        # 模拟XHS实例的manager.folder
        class MockManager:
            def __init__(self):
                self.folder = test_dir
        
        class MockXHS:
            def __init__(self):
                self.manager = MockManager()
        
        mock_xhs = MockXHS()
        
        # 导入并测试保存函数
        from source.application.batch_service import BatchProcessingService
        batch_service = BatchProcessingService()
        
        # 测试保存功能
        batch_service._save_text_data(mock_result, mock_xhs)
        
        # 检查文件是否创建
        expected_file = test_dir / mock_result['作品ID'] / "text.txt"
        if expected_file.exists():
            print(f"✅ 文本文件创建成功: {expected_file}")
            
            # 检查文件内容
            with open(expected_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            if saved_data == mock_result:
                print("✅ 文件内容正确")
            else:
                print("❌ 文件内容不匹配")
                return False
        else:
            print(f"❌ 文本文件未创建: {expected_file}")
            return False
        
        # 清理测试文件
        import shutil
        shutil.rmtree(test_dir)
        print("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        return False

def test_code_integration():
    """测试代码集成"""
    try:
        print("\n🔍 测试代码集成...")
        
        # 检查批量服务中的处理逻辑
        from source.application.batch_service import BatchProcessingService
        import inspect
        
        # 获取_process_batch_task方法的源代码
        source_code = inspect.getsource(BatchProcessingService._process_batch_task)
        
        # 检查是否包含保存文本数据的调用
        if '_save_text_data' in source_code:
            print("✅ 批量处理任务中包含文本数据保存调用")
        else:
            print("❌ 批量处理任务中缺少文本数据保存调用")
            return False
        
        # 检查API处理函数
        from source.application import app
        api_source = inspect.getsource(app.handle)
        
        if '_save_text_data_for_api' in api_source:
            print("✅ API处理函数中包含文本数据保存调用")
        else:
            print("❌ API处理函数中缺少文本数据保存调用")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试代码集成失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试文本数据保存功能...")
    print("=" * 60)
    
    # 运行所有测试
    tests = [
        test_imports,
        test_batch_service_text_save,
        test_api_text_save,
        test_mock_text_save,
        test_code_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print()
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！文本数据保存功能实现成功！")
        print("\n✨ 实现的功能:")
        print("1. ✅ 批量处理服务中添加了文本数据保存功能")
        print("2. ✅ 单个API调用中添加了文本数据保存功能")
        print("3. ✅ 文本数据以JSON格式保存到作品ID对应的文件夹")
        print("4. ✅ 文件名为 text.txt，编码为 UTF-8")
        print("5. ✅ 包含完整的作品信息（标题、作者、描述等）")
        print("6. ✅ 自动创建必要的文件夹结构")
        print("7. ✅ 包含错误处理和日志记录")
        
        print("\n📁 文件保存路径结构:")
        print("   下载根目录/")
        print("   └── 作品ID/")
        print("       └── text.txt  (JSON格式的作品信息)")
        
        return True
    else:
        print("❌ 部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

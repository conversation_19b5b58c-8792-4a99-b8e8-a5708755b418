# 批量下载API使用说明

本文档介绍如何使用新增的批量下载API功能。

## 功能特性

✅ **批量处理**: 支持一次提交多个小红书链接进行处理  
✅ **异步后台处理**: 请求立即返回，任务在后台异步执行  
✅ **并发控制**: 支持多个请求同时处理，不会阻塞接口  
✅ **异常处理**: 自动处理请求过程中的异常，提供详细错误信息  
✅ **任务状态跟踪**: 实时查询任务进度和处理结果  
✅ **任务管理**: 支持查看所有任务状态和历史记录  

## API 端点

### 1. 提交批量任务
```
POST /xhs/batch/
```

**请求体:**
```json
{
  "urls": [
    "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c7",
    "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c8"
  ],
  "download": true,
  "index": null,
  "cookie": null,
  "proxy": null,
  "skip": false
}
```

**响应:**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "批量任务已提交，正在后台处理",
  "status": "pending",
  "total_urls": 2
}
```

### 2. 查询任务状态
```
GET /xhs/batch/{task_id}
```

**响应:**
```json
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "total_urls": 2,
  "processed_urls": 1,
  "failed_urls": 0,
  "created_at": "2024-01-01T12:00:00",
  "updated_at": "2024-01-01T12:01:00",
  "results": [
    {
      "url": "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c7",
      "status": "success",
      "data": { /* 作品数据 */ }
    }
  ],
  "errors": []
}
```

### 3. 获取所有任务
```
GET /xhs/batch/
```

**响应:**
```json
[
  {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "total_urls": 2,
    "processed_urls": 2,
    "failed_urls": 0,
    "created_at": "2024-01-01T12:00:00",
    "updated_at": "2024-01-01T12:02:00",
    "results": [...],
    "errors": []
  }
]
```

## 任务状态说明

- `pending`: 任务已提交，等待处理
- `processing`: 任务正在处理中
- `completed`: 任务已完成
- `failed`: 任务失败

## 使用示例

### 1. 启动服务器
```bash
python server_example.py
```

### 2. 使用批量API
```bash
python batch_example.py
```

### 3. 手动调用API

使用curl提交批量任务:
```bash
curl -X POST "http://localhost:5556/xhs/batch/" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c7",
      "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c8"
    ],
    "download": true
  }'
```

查询任务状态:
```bash
curl "http://localhost:5556/xhs/batch/{task_id}"
```

## 配置说明

### 并发控制
- 默认最大并发任务数: 3
- 可在 `batch_service.py` 中修改 `max_concurrent_tasks` 参数

### 任务清理
- 默认保留任务记录24小时
- 可调用 `cleanup_old_tasks()` 方法清理旧任务

### 错误处理
- 单个URL处理失败不会影响其他URL
- 详细错误信息记录在任务的 `errors` 字段中
- 支持自动重试机制

## 注意事项

1. **服务器资源**: 批量处理会消耗较多系统资源，建议根据服务器性能调整并发数
2. **网络限制**: 过于频繁的请求可能触发小红书的反爬机制，建议适当控制请求频率
3. **存储空间**: 批量下载会占用大量磁盘空间，请确保有足够的存储空间
4. **任务监控**: 建议定期检查任务状态，及时处理失败的任务

## 故障排除

### 常见问题

1. **任务一直处于pending状态**
   - 检查工作线程是否正常启动
   - 查看服务器日志是否有错误信息

2. **部分URL处理失败**
   - 检查URL格式是否正确
   - 确认网络连接正常
   - 查看错误信息进行针对性处理

3. **服务器内存占用过高**
   - 减少并发任务数
   - 定期清理旧任务记录
   - 重启服务器释放内存

### 日志查看
服务器运行时会输出详细的处理日志，包括:
- 任务提交信息
- 处理进度更新
- 错误信息记录
- 工作线程状态

## 扩展功能

### 自定义处理逻辑
可以在 `batch_service.py` 中扩展处理逻辑:
- 添加自定义过滤器
- 实现特殊的文件命名规则
- 集成其他下载服务

### 监控和告警
可以集成监控系统:
- 任务完成率统计
- 错误率监控
- 性能指标收集
- 异常告警通知

"""
测试批量API功能的脚本
"""

import asyncio
import sys
from source import XHS


async def test_batch_functionality():
    """测试批量处理功能"""
    
    print("🧪 开始测试批量API功能...")
    print("=" * 50)
    
    # 测试配置
    work_path = "./test_downloads"
    folder_name = "batch_test"
    
    # 测试链接（请替换为有效的小红书链接）
    test_urls = [
        "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c7",
        "https://www.xiaohongshu.com/explore/64b7c9c0000000001203e5c8",
    ]
    
    try:
        # 创建XHS实例
        async with XHS(
            work_path=work_path,
            folder_name=folder_name,
            name_format="作品ID",
            timeout=10,
            max_retry=3,
            record_data=False,
            image_download=False,  # 测试时不下载文件
            video_download=False,
            live_download=False,
            download_record=False,
        ) as xhs:
            
            print("✅ XHS实例创建成功")
            
            # 导入批量服务
            from source.application.batch_service import batch_service
            from source.module.model import BatchExtractParams
            
            print("✅ 批量服务导入成功")
            
            # 创建批量参数
            params = BatchExtractParams(
                urls=test_urls,
                download=False,  # 测试时不下载
                skip=False
            )
            
            print(f"📋 准备处理 {len(test_urls)} 个链接")
            
            # 提交批量任务
            task_id = await batch_service.submit_batch_task(params, xhs)
            print(f"✅ 任务提交成功，任务ID: {task_id}")
            
            # 等待任务完成
            print("⏳ 等待任务完成...")
            max_wait_time = 60  # 最大等待60秒
            wait_time = 0
            
            while wait_time < max_wait_time:
                task_info = await batch_service.get_task_status(task_id)
                
                if not task_info:
                    print("❌ 无法获取任务状态")
                    break
                
                print(f"📊 任务状态: {task_info.status}, 进度: {task_info.processed_urls}/{task_info.total_urls}")
                
                if task_info.status in ['completed', 'failed']:
                    print(f"✅ 任务完成，最终状态: {task_info.status}")
                    
                    # 显示结果
                    print(f"📈 处理结果:")
                    print(f"  - 总链接数: {task_info.total_urls}")
                    print(f"  - 成功处理: {task_info.processed_urls}")
                    print(f"  - 失败数量: {task_info.failed_urls}")
                    
                    if task_info.errors:
                        print(f"❌ 错误信息:")
                        for error in task_info.errors:
                            print(f"  - {error}")
                    
                    if task_info.results:
                        print(f"📄 处理详情:")
                        for i, result in enumerate(task_info.results, 1):
                            print(f"  {i}. {result['url']}")
                            print(f"     状态: {result['status']}")
                            if result['status'] == 'failed':
                                print(f"     错误: {result.get('error', '未知')}")
                    
                    break
                
                await asyncio.sleep(2)
                wait_time += 2
            
            if wait_time >= max_wait_time:
                print("⏰ 等待超时")
            
            # 获取所有任务
            all_tasks = await batch_service.get_all_tasks()
            print(f"📋 当前共有 {len(all_tasks)} 个任务")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("✅ 批量API功能测试完成")
    return True


async def test_api_models():
    """测试API模型"""
    
    print("🧪 测试API模型...")
    
    try:
        from source.module.model import (
            BatchExtractParams, 
            BatchExtractResponse, 
            TaskInfo, 
            TaskStatus
        )
        
        # 测试BatchExtractParams
        params = BatchExtractParams(
            urls=["https://example.com/1", "https://example.com/2"],
            download=True
        )
        print(f"✅ BatchExtractParams: {params.urls}")
        
        # 测试BatchExtractResponse
        response = BatchExtractResponse(
            task_id="test-123",
            message="测试消息",
            status=TaskStatus.PENDING,
            total_urls=2
        )
        print(f"✅ BatchExtractResponse: {response.task_id}")
        
        # 测试TaskInfo
        task_info = TaskInfo(
            task_id="test-123",
            status=TaskStatus.PENDING,
            total_urls=2,
            processed_urls=0,
            failed_urls=0,
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-01T00:00:00"
        )
        print(f"✅ TaskInfo: {task_info.task_id}")
        
        print("✅ API模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    
    print("🚀 XHS-Downloader 批量API测试")
    print("=" * 50)
    
    # 测试API模型
    model_test_result = await test_api_models()
    
    if not model_test_result:
        print("❌ API模型测试失败，跳过功能测试")
        return
    
    print()
    
    # 测试批量功能
    functionality_test_result = await test_batch_functionality()
    
    print()
    print("=" * 50)
    
    if model_test_result and functionality_test_result:
        print("🎉 所有测试通过！")
    else:
        print("❌ 部分测试失败")
        sys.exit(1)


if __name__ == "__main__":
    print("⚠️  注意：请确保测试链接是有效的小红书链接")
    print("⚠️  测试过程中不会下载实际文件")
    print()
    
    # 运行测试
    asyncio.run(main())

[project]
name = "XHS-Downloader"
version = "2.6"
description = "小红书（XiaoHongShu、RedNote）链接提取/作品采集工具：提取账号发布、收藏、点赞、专辑作品链接；提取搜索结果作品、用户链接；采集小红书作品信息；提取小红书作品下载地址；下载小红书无水印作品文件"
authors = [
    { name = "JoeanAmier", email = "<EMAIL>" },
]
readme = "README.md"
license = "GPL-3.0"
requires-python = ">=3.12,<3.13"
dependencies = [
    "aiofiles>=24.1.0",
    "aiosqlite>=0.21.0",
    "click>=8.1.8",
    "emoji>=2.14.1",
    "fastapi>=0.115.9",
    "httpx[socks]>=0.28.1",
    "lxml>=5.3.1",
    "pyperclip>=1.9.0",
    "pyside6>=6.6.0",
    "pyyaml>=6.0.2",
    "qasync>=0.24.0",
    "rookiepy>=0.5.6",
    "textual>=3.1.0",
    "uvicorn>=0.34.0",
]

[project.urls]
Repository = "https://github.com/JoeanAmier/XHS-Downloader"

[tool.uv.pip]
index-url = "https://mirrors.ustc.edu.cn/pypi/simple"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"

[dependency-groups]
dev = [
    "textual-dev>=1.7.0",
]

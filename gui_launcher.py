#!/usr/bin/env python3
"""
XHS-Downloader GUI启动器
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        import qasync
    except ImportError:
        missing_deps.append("qasync")
    
    if missing_deps:
        print("❌ 缺少以下依赖项:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装依赖:")
        print("pip install PySide6 qasync")
        return False
    
    return True


def main():
    """主函数"""
    print("🚀 启动 XHS-Downloader GUI...")
    
    # 检查依赖项
    if not check_dependencies():
        return 1
    
    try:
        # 导入GUI应用程序
        from source.GUI.app import main as gui_main
        
        print("✅ 依赖项检查通过")
        print("📱 正在启动图形界面...")
        
        # 运行GUI应用程序
        return gui_main()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保项目结构完整且依赖项已安装")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())

# XHS-Downloader GUI 使用说明

## 概述

XHS-Downloader GUI 是基于 PySide6 开发的图形界面版本，提供了直观易用的界面来配置和管理小红书内容下载任务。

## 功能特性

### ✨ 主要功能

- **🎨 扁平化设计**: 现代化的用户界面，简洁美观
- **⚙️ 动态配置**: 通过界面动态配置所有启动选项
- **🚀 一键启动**: 点击按钮即可启动服务
- **📊 任务监控**: 实时显示任务进度和状态
- **📝 日志查看**: 实时查看服务器运行日志
- **💾 配置保存**: 自动保存和加载配置设置

### 🔧 配置选项

#### 基本配置
- **下载路径**: 文件保存位置
- **文件夹名称**: 下载文件夹名称
- **命名格式**: 文件命名规则
- **功能开关**: 图片/视频/动图下载控制
- **存储选项**: 文件夹模式、按作者分类等

#### 网络配置
- **User-Agent**: 自定义用户代理
- **Cookie**: 小红书登录凭证
- **代理设置**: HTTP/SOCKS代理配置
- **超时设置**: 请求超时时间
- **重试次数**: 失败重试配置

#### 下载配置
- **下载块大小**: 控制下载性能
- **图片格式**: 支持多种图片格式
- **语言设置**: 界面语言选择

#### 服务器配置
- **服务器地址**: API服务监听地址
- **服务器端口**: API服务端口
- **日志级别**: 日志详细程度

## 安装依赖

### 1. 安装 Python 依赖

```bash
# 安装 PySide6 (Qt6 Python绑定)
pip install PySide6

# 安装 qasync (Qt异步支持)
pip install qasync

# 或者一次性安装
pip install PySide6 qasync
```

### 2. 更新项目依赖

如果使用 uv 包管理器：

```bash
uv pip install PySide6 qasync
```

## 启动方式

### 方法一：使用启动器（推荐）

```bash
python gui_launcher.py
```

### 方法二：直接启动

```bash
python -m source.GUI.app
```

### 方法三：测试模式

```bash
# 先运行测试确保环境正常
python test_gui.py

# 测试通过后启动GUI
python gui_launcher.py
```

## 使用指南

### 1. 配置设置

1. **启动应用程序**
   - 运行 `python gui_launcher.py`
   - 等待界面加载完成

2. **基本配置**
   - 在"基本配置"标签页设置下载路径
   - 选择需要下载的内容类型（图片/视频/动图）
   - 配置文件命名格式

3. **网络配置**
   - 如需要，设置代理服务器
   - 输入小红书Cookie（提高成功率）
   - 调整超时和重试设置

4. **保存配置**
   - 点击"保存配置"按钮
   - 配置会自动保存到 `xhs_gui_config.json`

### 2. 启动服务

1. **验证配置**
   - 确保所有必要配置已填写
   - 检查下载路径是否存在

2. **启动服务器**
   - 点击"启动服务"按钮
   - 等待服务器启动完成
   - 查看状态栏确认运行状态

3. **监控任务**
   - 服务启动后，任务列表会自动刷新
   - 可以实时查看任务进度和状态

### 3. 任务管理

1. **查看任务列表**
   - 任务表格显示所有批量下载任务
   - 包含任务ID、状态、进度等信息

2. **查看任务详情**
   - 点击任务行查看详细信息
   - 包含错误信息和处理结果

3. **管理任务**
   - 使用"刷新"按钮手动更新任务状态
   - 使用"清空已完成"清理历史任务

### 4. 停止服务

1. **正常停止**
   - 点击"停止服务"按钮
   - 等待服务器完全停止

2. **强制退出**
   - 关闭窗口时会提示确认
   - 确认后自动停止服务器

## 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                    XHS-Downloader GUI                      │
├─────────────────┬───────────────────────────────────────────┤
│   配置管理      │              任务管理                     │
│                 │                                           │
│ ┌─────────────┐ │ ┌───────────────────────────────────────┐ │
│ │  基本配置   │ │ │           任务列表                    │ │
│ │  网络配置   │ │ │                                       │ │
│ │  下载配置   │ │ │  ID │状态│进度│时间│...              │ │
│ │  服务器配置 │ │ │                                       │ │
│ └─────────────┘ │ └───────────────────────────────────────┘ │
│                 │                                           │
│ ┌─────────────┐ │ ┌───────────────────────────────────────┐ │
│ │ 启动服务    │ │ │           任务详情                    │ │
│ │ 停止服务    │ │ │                                       │ │
│ └─────────────┘ │ └───────────────────────────────────────┘ │
│                 │                                           │
│                 │ ┌───────────────────────────────────────┐ │
│                 │ │           服务器日志                  │ │
│                 │ └───────────────────────────────────────┘ │
├─────────────────┴───────────────────────────────────────────┤
│ 状态栏: 服务器状态 | 版本信息                              │
└─────────────────────────────────────────────────────────────┘
```

## 配置文件

配置文件 `xhs_gui_config.json` 包含所有设置：

```json
{
  "work_path": "D:/Download",
  "folder_name": "XHS_Batch_Downloads",
  "name_format": "作品ID",
  "user_agent": "",
  "cookie": "",
  "proxy": "",
  "timeout": 10,
  "max_retry": 5,
  "chunk": 10485760,
  "record_data": false,
  "image_format": "JPEG",
  "folder_mode": false,
  "image_download": true,
  "video_download": true,
  "live_download": true,
  "download_record": true,
  "author_archive": false,
  "write_mtime": true,
  "language": "zh_CN",
  "read_cookie": "",
  "server_host": "0.0.0.0",
  "server_port": 5556,
  "log_level": "info"
}
```

## API 使用

GUI启动服务后，可以通过以下API进行批量下载：

### 提交批量任务

```bash
curl -X POST "http://localhost:5556/xhs/batch/" \
  -H "Content-Type: application/json" \
  -d '{
    "urls": [
      "https://www.xiaohongshu.com/explore/...",
      "https://www.xiaohongshu.com/explore/..."
    ],
    "download": true
  }'
```

### 查询任务状态

```bash
curl "http://localhost:5556/xhs/batch/{task_id}"
```

### 获取所有任务

```bash
curl "http://localhost:5556/xhs/batch/"
```

## 故障排除

### 常见问题

1. **启动失败**
   - 检查Python版本（需要3.12+）
   - 确认PySide6和qasync已安装
   - 检查端口是否被占用

2. **配置错误**
   - 验证下载路径是否存在
   - 检查端口号是否在有效范围内
   - 确认代理设置格式正确

3. **任务失败**
   - 检查网络连接
   - 验证Cookie是否有效
   - 查看错误日志获取详细信息

### 调试模式

启用调试模式获取更多信息：

```bash
# 设置日志级别为debug
# 在GUI中将"日志级别"设置为"debug"

# 或者通过环境变量
export XHS_LOG_LEVEL=debug
python gui_launcher.py
```

## 开发说明

### 项目结构

```
source/GUI/
├── __init__.py          # 模块初始化
├── app.py              # GUI应用程序入口
├── main_window.py      # 主窗口
├── config_widget.py    # 配置界面组件
├── task_widget.py      # 任务管理组件
├── config_manager.py   # 配置管理器
├── server_manager.py   # 服务器管理器
└── styles.py           # 界面样式
```

### 扩展开发

1. **添加新配置项**
   - 在 `config_manager.py` 中的 `XHSConfig` 类添加字段
   - 在 `config_widget.py` 中添加对应的界面控件
   - 更新保存和加载逻辑

2. **自定义样式**
   - 修改 `styles.py` 中的样式定义
   - 支持自定义颜色主题

3. **添加新功能**
   - 创建新的组件文件
   - 在主窗口中集成新组件

## 许可证

本项目遵循 GPL-3.0 许可证。
